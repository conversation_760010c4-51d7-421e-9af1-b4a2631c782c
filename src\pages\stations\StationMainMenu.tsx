import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  ShoppingCartIcon,
  CoffeeIcon,
  GlassWaterIcon,
  WineIcon,
  UtensilsIcon,
  BeefIcon,
  IceCreamBowlIcon,
  SoupIcon,
  SaladIcon,
  PizzaIcon,
  CircleDotIcon,
  ArrowLeft,
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Link } from "react-router-dom";
import StationOrderItem from "@/pages/stations/components/StationOrderItem";
import { useGetMenuGroupsQuery } from "@/redux/slices/menuGroup";
import { menuGroupTypes } from "@/types/menu";
import { useLazyGetMenuSubGroupsQuery } from "@/redux/slices/subGroup";
import { useLazyGetMenusQuery } from "@/redux/slices/menuMake";
import { useLazyGetTablesQuery } from "@/redux/slices/tables";
import { orderTableTypes } from "@/types/order";
import { toast } from "sonner";
import { useAddOrderMutation } from "@/redux/slices/order";

interface MenuItem {
  id: string;
  name: string;
  unit_price: number;
  price: number;
  notes?: string;
  extras?: Array<{ label: string; price: string; id: string }>;
}

interface OrderItem extends MenuItem {
  quantity: number;
}

const StationMainMenu: React.FC = () => {
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
  const [selectedSubgroup, setSelectedSubgroup] = useState<string | null>(null);
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [noteText, setNoteText] = useState("");
  const [isMobileOrderOpen, setIsMobileOrderOpen] = useState(false);
  const [orderType, setOrderType] = useState<string>("");
  const [selectedTable, setSelectedTable] = useState<string>("");
  const [discountCode, setDiscountCode] = useState<string>("");
  const [appliedDiscount, setAppliedDiscount] = useState<number>(0);
  const [discountError, setDiscountError] = useState<string>("");

  const { data: menuGroups, isLoading: mgLoading } = useGetMenuGroupsQuery({});

  // Deduplicate menu groups to prevent React StrictMode duplication issues
  const uniqueMenuGroups = React.useMemo(() => {
    if (!menuGroups?.data?.results) return [];

    const seen = new Set();
    return menuGroups.data.results.filter((group: menuGroupTypes) => {
      if (seen.has(group.id)) {
        return false;
      }
      seen.add(group.id);
      return true;
    });
  }, [menuGroups?.data?.results]);
  const [
    getSubgroups,
    { data: subgroups, isLoading: sgLoading, isFetching: sgFetching },
  ] = useLazyGetMenuSubGroupsQuery();
  const [getMenuItems, { data: menuItems, isLoading: mILoading }] =
    useLazyGetMenusQuery();
  const [getTables, { data: tables, isLoading: tLoading }] =
    useLazyGetTablesQuery();
  const [punchOrder, { isLoading: loading }] = useAddOrderMutation();

  useEffect(() => {
    if (orderType === "Dine In") {
      getTables({});
    }
  }, [orderType]);

  useEffect(() => {
    if (selectedGroup) {
      getSubgroups({ group: selectedGroup });
    }
  }, [selectedGroup]);

  useEffect(() => {
    if (selectedSubgroup) {
      getMenuItems({ menu_subgroup: selectedSubgroup });
    }
  }, [selectedSubgroup]);

  const addToOrder = (item: MenuItem) => {
    setOrderItems((prev) => {
      const existing = prev.find((i) => i.id === item.id);
      if (existing) {
        return prev.map((i) =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
        );
      }
      return [...prev, { ...item, quantity: 1, unit_price: item.price }];
    });
  };

  const updateQuantity = (itemId: string, delta: number) => {
    setOrderItems(
      (prev) =>
        prev
          .map((item) => {
            if (item.id === itemId) {
              const newQuantity = Math.max(0, item.quantity + delta);
              return newQuantity === 0
                ? null
                : { ...item, quantity: newQuantity };
            }
            return item;
          })
          .filter(Boolean) as OrderItem[]
    );
  };

  const calculateItemTotal = (item: OrderItem) => {
    const basePrice = parseFloat(item.price.toString()) * item.quantity;
    return basePrice;
  };

  const calculateOrderTotal = () => {
    return orderItems.reduce((sum, item) => sum + calculateItemTotal(item), 0);
  };

  // Backward tax calculation: calculate subtotal from total (assuming total includes tax)
  const calculateTaxBackward = (totalWithTax: number) => {
    // If total = subtotal * 1.16, then subtotal = total / 1.16
    const subtotal = totalWithTax / 1.16;
    const taxAmount = totalWithTax - subtotal;
    return { subtotal, taxAmount };
  };

  // Calculate final totals with discount
  const calculateFinalTotals = () => {
    const baseTotal = calculateOrderTotal();
    const discountAmount = (baseTotal * appliedDiscount) / 100;
    const totalAfterDiscount = baseTotal - discountAmount;

    // Apply backward tax calculation
    const { subtotal, taxAmount } = calculateTaxBackward(totalAfterDiscount);

    return {
      baseTotal,
      discountAmount,
      subtotal,
      taxAmount,
      finalTotal: totalAfterDiscount
    };
  };

  // Validate and apply discount code
  const applyDiscountCode = () => {
    setDiscountError("");

    // Simple discount code validation (you can expand this)
    const validCodes = {
      "SAVE10": 10,
      "SAVE15": 15,
      "SAVE20": 20,
      "WELCOME5": 5,
      "STUDENT": 12
    };

    const code = discountCode.toUpperCase().trim();

    if (validCodes[code as keyof typeof validCodes]) {
      setAppliedDiscount(validCodes[code as keyof typeof validCodes]);
      setDiscountError("");
    } else if (code === "") {
      setDiscountError("Please enter a discount code");
    } else {
      setDiscountError("Invalid discount code");
    }
  };

  // Remove applied discount
  const removeDiscount = () => {
    setAppliedDiscount(0);
    setDiscountCode("");
    setDiscountError("");
  };

  // handle punch order
  const handlePunchOrder = async () => {
    if (orderType == "Dine In" && selectedTable == "") {
      toast.error("Table number is required!!");
      return;
    }

    // remove selected table when its takeway
    if (orderType == "Takeaway") {
      setSelectedTable("");
    }
    console.log("table", selectedTable);
    console.log("orderType", orderType);

    // Use new calculation logic with backward tax calculation
    const totals = calculateFinalTotals();
    let sub_total = totals.subtotal.toFixed(2);
    let tax_amount = totals.taxAmount.toFixed(2);
    let total_amount = totals.finalTotal.toFixed(2);
    let discount_amount = totals.discountAmount.toFixed(2);

    let order_items = orderItems.map((item) => ({
      menu_item: item.id,
      unit_price: parseFloat(item.unit_price.toString()).toFixed(2),
      line_total: item.price,
      quantity: item.quantity,
      modifiers: item.extras || [],
      special_instructions: item.notes || "",
    }));

    const formData = {
      order_type: orderType,
      table_number: selectedTable,
      order_items,
      sub_total,
      tax_amount,
      total_amount,
      discount_code: discountCode || null,
      discount_amount,
      discount_percentage: appliedDiscount,
    };
    console.log("formData", formData);

    try {
      const res = await punchOrder(formData).unwrap();
      if (res?.data) {
        toast.success(res?.message);
        setOrderItems([]);
        setSelectedTable("");
        setOrderType("");
        setSelectedGroup(null);
        setSelectedSubgroup(null);
        setNoteText("");
        setDiscountCode("");
        setAppliedDiscount(0);
        setDiscountError("");
      }
    } catch (error: any) {
      toast.error("could punch order something went wrong");
      console.log("error", error);
      console.log("error data", error?.data);
      console.log("error data eror", error?.data?.error);
    }
  };

  return (
    <div className="flex flex-col lg:flex-row min-h-screen bg-background">
      <div className="w-full lg:w-1/4 p-2 lg:p-4 lg:border-r border-border/50">
        <Link to="/stations/home">
          <Button className="flex items-center justify-center gap-2 w-full mb-4 h-12 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:scale-105 transition-all duration-200">
            <ArrowLeft className="h-4 w-4" />
            Back to Main Menu
          </Button>
        </Link>

        <Card className="shadow-lg border-border/50">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg lg:text-xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Menu Groups
            </CardTitle>
          </CardHeader>
          <CardContent>
            {mgLoading ? (
              <div className="flex justify-center items-center py-8">
                <div className="text-muted-foreground">Loading menu groups...</div>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-2 gap-3">
                {uniqueMenuGroups?.map((group: menuGroupTypes) => (
                <Button
                  key={group?.id}
                  variant={selectedGroup === group?.id ? "default" : "outline"}
                  onClick={() => {
                    setSelectedGroup(group?.id);
                    setSelectedSubgroup(null);
                  }}
                  className={`
                    w-full p-4 lg:p-8 text-sm lg:text-base
                    transition-all duration-300
                    hover:scale-105 hover:shadow-lg
                    flex flex-row lg:flex-col items-center justify-center gap-2
                    border-border/50
                    ${
                      selectedGroup === group?.id
                        ? "bg-gradient-to-br from-primary to-primary/90 text-primary-foreground shadow-lg scale-105"
                        : "hover:border-primary/50 hover:bg-primary/5"
                    }
                  `}
                >
                  <span className="text-xl">
                    {group?.sales_category === "DRINKS"
                      ? "🥤"
                      : group?.sales_category === "FOOD"
                      ? "🍽️"
                      : group?.sales_category === "COMBOS"
                      ? "🥗"
                      : group?.sales_category === "DESSERTS"
                      ? "🍰"
                      : "🎉 "}
                  </span>
                  <span className="font-medium">{group?.name}</span>
                </Button>
              ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="w-full lg:w-2/4 p-2 lg:p-4">
        {selectedGroup ? (
          <div className="space-y-4 my-4">
            <Card className="shadow-lg border-border/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg lg:text-xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Subgroups
                </CardTitle>
              </CardHeader>
              <CardContent>
                {sgLoading || sgFetching ? (
                  <div>Loading...</div>
                ) : !subgroups || subgroups?.data?.results.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                    <div className="text-4xl lg:text-6xl mb-4 opacity-50">
                      📋
                    </div>
                    <h3 className="text-xl lg:text-2xl font-semibold text-foreground mb-2">
                      No Subgroups Available
                    </h3>
                    <p className="text-muted-foreground max-w-md text-sm lg:text-base">
                      Please select a menu group or check back later for
                      available subgroups.
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    {subgroups?.data?.results.map((subgroup: any) => (
                      <Button
                        key={subgroup?.id}
                        variant={
                          selectedSubgroup === subgroup?.id
                            ? "default"
                            : "outline"
                        }
                        onClick={() => setSelectedSubgroup(subgroup?.id)}
                        className={`
                          w-full h-16 lg:h-24 flex flex-col items-center justify-center gap-1 lg:gap-2
                          transition-all duration-300 hover:scale-105 hover:shadow-lg border-border/50
                          ${
                            selectedSubgroup === subgroup?.id
                              ? "bg-gradient-to-br from-primary to-primary/90 text-primary-foreground shadow-lg scale-105"
                              : "hover:border-primary/50 hover:bg-primary/5"
                          }
                        `}
                      >
                        <div className="text-lg lg:text-2xl">
                          {subgroup?.name.includes("Hot Drinks") ? (
                            <CoffeeIcon className="h-6 w-6" />
                          ) : subgroup?.name.includes("Cold Drinks") ? (
                            <GlassWaterIcon className="h-6 w-6" />
                          ) : subgroup?.name.includes("Cocktails") ||
                            subgroup?.name.includes("Beer") ||
                            subgroup?.name.includes("Wine") ? (
                            <WineIcon className="h-6 w-6" />
                          ) : subgroup?.name.includes("Pasta") ? (
                            <UtensilsIcon className="h-6 w-6" />
                          ) : subgroup?.name.includes("Rice") ? (
                            <IceCreamBowlIcon className="h-6 w-6" />
                          ) : subgroup?.name.includes("Grilled") ||
                            subgroup?.name.includes("Meat") ? (
                            <BeefIcon className="h-6 w-6" />
                          ) : subgroup?.name.includes("Salads") ? (
                            <SaladIcon className="h-6 w-6" />
                          ) : subgroup?.name.includes("Soups") ? (
                            <SoupIcon className="h-6 w-6" />
                          ) : subgroup?.name.includes("Starters") ? (
                            <PizzaIcon className="h-6 w-6" />
                          ) : (
                            <CircleDotIcon className="h-6 w-6" />
                          )}
                        </div>
                        <span className="font-medium text-xs lg:text-sm text-wrap">
                          {subgroup?.name}
                        </span>
                      </Button>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        ) : (
          <Card className="my-4 shadow-lg border-border/50">
            <CardContent className="py-8">
              <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                <div className="text-4xl lg:text-6xl mb-4 opacity-50">📋</div>
                <h3 className="text-xl lg:text-2xl font-semibold text-foreground mb-2">
                  Select a Menu Group
                </h3>
                <p className="text-muted-foreground max-w-md text-sm lg:text-base">
                  Choose a menu group above to view available subgroups and
                  products.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
        {selectedSubgroup ? (
          <Card className="shadow-lg border-border/50">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg lg:text-xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Products
              </CardTitle>
            </CardHeader>
            <CardContent>
              {menuItems?.data?.total_data <= 0 || !menuItems?.data ? (
                <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                  <div className="text-4xl lg:text-6xl mb-4 opacity-50">🍽️</div>
                  <h3 className="text-xl lg:text-2xl font-semibold text-foreground mb-2">
                    No Products Available
                  </h3>
                  <p className="text-muted-foreground max-w-md text-sm lg:text-base">
                    Looks like this category is empty. Check back later for
                    exciting new additions to our menu!
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {menuItems?.data?.results?.map((product: any) => (
                    <Button
                      key={product?.id}
                      variant="outline"
                      onClick={() => addToOrder(product)}
                      className={`
                        w-full h-20 lg:h-24 p-3 flex flex-col justify-center items-center gap-2
                        transition-all duration-300 hover:scale-105 hover:shadow-lg border-border/50
                        ${
                          orderItems.some((item) => item.id === product?.id)
                            ? "bg-gradient-to-br from-secondary/20 to-secondary/10 border-secondary text-secondary-foreground shadow-lg scale-105"
                            : "hover:border-primary/50 hover:bg-primary/5"
                        }
                      `}
                    >
                      <span className="text-xs lg:text-sm font-semibold text-center leading-tight text-wrap">
                        {product?.name}
                      </span>
                      <span className="text-xs lg:text-sm font-bold text-primary">
                        Kshs. {product?.price}
                      </span>
                    </Button>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          <Card className="shadow-lg border-border/50">
            <CardContent className="py-8">
              <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                <div className="text-4xl lg:text-6xl mb-4 opacity-50">🍽️</div>
                <h3 className="text-xl lg:text-2xl font-semibold text-foreground mb-2">
                  Select a Subgroup
                </h3>
                <p className="text-muted-foreground max-w-md text-sm lg:text-base">
                  Choose a subgroup to view available products and add them to
                  your order.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="lg:hidden fixed bottom-4 right-6 z-50">
        <Button
          onClick={() => setIsMobileOrderOpen(!isMobileOrderOpen)}
          className="rounded-full w-16 h-16 shadow-2xl bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary hover:scale-110 transition-all duration-300"
        >
          <div className="relative">
            <ShoppingCartIcon className="h-6 w-6" />
            {orderItems.length > 0 && (
              <div className="absolute -top-3 -right-3 bg-destructive text-destructive-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold animate-pulse">
                {orderItems.length}
              </div>
            )}
          </div>
        </Button>
      </div>

      <div
        className={`
        ${isMobileOrderOpen ? "fixed inset-0 z-40 bg-background" : "hidden"}
        lg:block lg:relative lg:w-1/4 lg:p-4 lg:border-l border-border/50
        ${isMobileOrderOpen ? "p-4" : ""}
      `}
      >
        <div className="lg:hidden flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-foreground">Current Order</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsMobileOrderOpen(false)}
            className="hover:scale-105 transition-all duration-200"
          >
            ✕
          </Button>
        </div>

        <Card className="h-full shadow-lg border-border/50">
          <CardHeader className="hidden lg:block sticky top-0 bg-card z-10 border-b border-border/50">
            <CardTitle className="text-lg md:text-xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Current Order
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <ScrollArea className="h-[calc(100vh-400px)] lg:h-[calc(100vh-400px)] pt-2">
              {orderItems.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="text-4xl mb-4 opacity-50">🛒</div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    Your cart is empty
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Add some delicious items to get started!
                  </p>
                </div>
              ) : (
                orderItems.map((item) => (
                  <StationOrderItem
                    key={item.id}
                    item={item}
                    calculateItemTotal={calculateItemTotal}
                    updateQuantity={updateQuantity}
                    setOrderItems={setOrderItems}
                    setNoteText={setNoteText}
                    noteText={noteText}
                  />
                ))
              )}
            </ScrollArea>

            {orderItems.length > 0 && (
              <>
                {/* Discount Code Section */}
                <div className="mt-4 pt-4 border-t border-border/50 space-y-3">
                  <h4 className="font-semibold text-sm">Discount Code</h4>
                  {appliedDiscount === 0 ? (
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <input
                          type="text"
                          placeholder="Enter discount code"
                          value={discountCode}
                          onChange={(e) => setDiscountCode(e.target.value)}
                          className="flex-1 p-2 text-sm border rounded-md border-border/50 bg-background"
                          onKeyDown={(e) => e.key === 'Enter' && applyDiscountCode()}
                        />
                        <Button
                          size="sm"
                          onClick={applyDiscountCode}
                          className="px-4"
                        >
                          Apply
                        </Button>
                      </div>
                      {discountError && (
                        <p className="text-xs text-destructive">{discountError}</p>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center justify-between bg-green-50 dark:bg-green-900/20 p-2 rounded-md">
                      <span className="text-sm text-green-700 dark:text-green-300">
                        {discountCode} ({appliedDiscount}% off) applied
                      </span>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={removeDiscount}
                        className="text-xs h-6 px-2"
                      >
                        Remove
                      </Button>
                    </div>
                  )}
                </div>

                {/* Order Summary */}
                <div className="mt-4 pt-4 border-t border-border/50 sticky bottom-0 bg-card space-y-2">
                  {(() => {
                    const totals = calculateFinalTotals();
                    return (
                      <>
                        <div className="flex justify-between text-sm text-muted-foreground">
                          <span>Items Total:</span>
                          <span>Ksh. {totals.baseTotal.toFixed(2)}</span>
                        </div>
                        {appliedDiscount > 0 && (
                          <div className="flex justify-between text-sm text-green-600 dark:text-green-400">
                            <span>Discount ({appliedDiscount}%):</span>
                            <span>-Ksh. {totals.discountAmount.toFixed(2)}</span>
                          </div>
                        )}
                        <div className="flex justify-between text-sm text-muted-foreground">
                          <span>Subtotal (excl. tax):</span>
                          <span>Ksh. {totals.subtotal.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between text-sm text-destructive dark:text-destructive">
                          <span>Tax (16%):</span>
                          <span>+Ksh. {totals.taxAmount.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between text-lg font-bold pt-2 border-t border-border/50">
                          <span>Total:</span>
                          <span className="text-primary">
                            Ksh. {totals.finalTotal.toFixed(2)}
                          </span>
                        </div>
                      </>
                    );
                  })()}
                </div>

                <div className="my-4 border-t pt-4">
                  <div className="flex gap-2 mb-2">
                    <Button
                      type="button"
                      variant={orderType === "Dine In" ? "default" : "outline"}
                      className="w-1/2 border-primary"
                      onClick={() => setOrderType("Dine In")}
                    >
                      Dine In
                    </Button>
                    <Button
                      type="button"
                      variant={orderType === "Takeaway" ? "default" : "outline"}
                      className="w-1/2 border-primary"
                      onClick={() => setOrderType("Takeaway")}
                    >
                      Takeaway
                    </Button>
                  </div>
                  {orderType === "Dine In" && (
                    <div className="mt-3 space-y-1">
                      <label className="font-bold pl-1" htmlFor="table_number">
                        Select Table
                      </label>

                      <select
                        className="w-full p-2 border rounded-md border-border/50"
                        value={selectedTable}
                        onChange={(e) => setSelectedTable(e.target.value)}
                      >
                        <option value="">Select Table</option>
                        {tables?.data?.results?.map(
                          (table: orderTableTypes) => (
                            <option key={table.id} value={table.id}>
                              {table.table_number}
                            </option>
                          )
                        )}
                      </select>
                    </div>
                  )}
                </div>

                <div className="flex flex-col gap-3 my-4 pt-4 border-t border-border/50">
                  <Button
                    className="w-full h-12 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-600 text-white font-semibold hover:scale-105 transition-all duration-200 shadow-lg"
                    disabled={orderItems.length === 0}
                    onClick={() => handlePunchOrder()}
                  >
                    Punch Order
                  </Button>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="w-1/2 hover:scale-105 transition-all duration-200"
                      onClick={() => {
                        setOrderItems([]);
                        setDiscountCode("");
                        setAppliedDiscount(0);
                        setDiscountError("");
                      }}
                    >
                      Cancel Order
                    </Button>
                    <Button
                      variant="destructive"
                      className="w-1/2 hover:scale-105 transition-all duration-200"
                      asChild
                    >
                      <Link to="/stations-auth">Log Out</Link>
                    </Button>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StationMainMenu;
